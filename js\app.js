// Aplicação Principal
class App {
    constructor() {
        this.currentPage = 'dashboard';
        this.currentUser = null;
        this.data = {
            clientes: [],
            produtos: [],
            funcionarios: [],
            agendamentos: [],
            usuarios: []
        };

        this.init();
    }

    async init() {
        // Verificar autenticação
        if (!this.checkAuth()) {
            window.location.href = 'login.html';
            return;
        }

        this.setupNavigation();
        this.setupForms();
        this.setupPermissions();
        await this.loadInitialData();
        this.initializeCalendar();
        this.loadDashboard();
    }

    checkAuth() {
        const user = localStorage.getItem('user');
        const token = localStorage.getItem('token');

        if (!user || !token) {
            return false;
        }

        try {
            this.currentUser = JSON.parse(user);

            // Verificar se o token ainda é válido (verificação básica)
            const tokenData = this.parseJWT(token);
            if (tokenData && tokenData.exp && Date.now() >= tokenData.exp * 1000) {
                console.log('Token expirado');
                this.logout();
                return false;
            }

            return true;
        } catch (error) {
            console.error('Erro ao verificar autenticação:', error);
            this.logout();
            return false;
        }
    }

    parseJWT(token) {
        try {
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
            return JSON.parse(jsonPayload);
        } catch (error) {
            return null;
        }
    }

    setupPermissions() {
        // Mostrar/ocultar elementos baseado nas permissões
        const adminOnlyElements = document.querySelectorAll('.admin-only');
        const gerenteOnlyElements = document.querySelectorAll('.gerente-only');

        if (this.currentUser.tipo !== 'admin') {
            adminOnlyElements.forEach(el => el.style.display = 'none');
        }

        if (!['admin', 'gerente'].includes(this.currentUser.tipo)) {
            gerenteOnlyElements.forEach(el => el.style.display = 'none');
        }
    }

    getUserTypeLabel(tipo) {
        const labels = {
            'admin': 'Administrador',
            'gerente': 'Gerente',
            'vendedor': 'Vendedor'
        };
        return labels[tipo] || tipo;
    }

    logout() {
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        window.location.href = 'login.html';
    }

    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                this.navigateTo(page);
            });
        });
    }

    navigateTo(page) {
        // Atualizar navegação ativa
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-page="${page}"]`).classList.add('active');

        // Esconder todas as páginas
        document.querySelectorAll('.page').forEach(p => {
            p.classList.remove('active');
        });

        // Mostrar página selecionada
        const pageElement = document.getElementById(`${page}-page`);
        if (pageElement) {
            pageElement.classList.add('active');
            this.currentPage = page;
            this.loadPage(page);
        }
    }

    async loadPage(page) {
        switch (page) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'agendamentos':
                this.loadAgendamentosPage();
                break;
            case 'clientes':
                this.loadClientesPage();
                break;
            case 'produtos':
                this.loadProdutosPage();
                break;
            case 'funcionarios':
                this.loadFuncionariosPage();
                break;
            case 'relatorios':
                this.loadRelatoriosPage();
                break;
            case 'usuarios':
                this.loadUsuariosPage();
                break;
            case 'configuracoes':
                this.loadConfiguracoes();
                break;
        }
    }

    async loadInitialData() {
        try {
            const promises = [
                api.getClientes(),
                api.getProdutos(),
                api.getFuncionarios(),
                api.getAgendamentos()
            ];

            // Adicionar usuários apenas se for admin
            if (this.currentUser && this.currentUser.tipo === 'admin') {
                promises.push(api.getUsuarios());
            }

            const results = await Promise.all(promises);

            this.data.clientes = results[0];
            this.data.produtos = results[1];
            this.data.funcionarios = results[2];
            this.data.agendamentos = results[3];

            // Usuários apenas para admin
            if (this.currentUser && this.currentUser.tipo === 'admin' && results[4]) {
                this.data.usuarios = results[4];
            }

            this.populateSelects();
        } catch (error) {
            console.error('Erro ao carregar dados iniciais:', error);
            notifications.error('Erro ao carregar dados do sistema');
        }
    }

    populateSelects() {
        // Popular select de clientes
        const clienteSelects = document.querySelectorAll('select[name="cliente_id"]');
        clienteSelects.forEach(select => {
            select.innerHTML = '<option value="">Selecione um cliente</option>';
            this.data.clientes.forEach(cliente => {
                select.innerHTML += `<option value="${cliente.id}">${cliente.nome}</option>`;
            });
        });

        // Popular select de funcionários
        const funcionarioSelects = document.querySelectorAll('select[name="funcionario_id"]');
        funcionarioSelects.forEach(select => {
            select.innerHTML = '<option value="">Selecione um funcionário</option>';
            this.data.funcionarios.forEach(funcionario => {
                select.innerHTML += `<option value="${funcionario.id}">${funcionario.nome}</option>`;
            });
        });

        // Popular select de produtos
        const produtoSelects = document.querySelectorAll('select[name="produto_id"]');
        produtoSelects.forEach(select => {
            select.innerHTML = '<option value="">Selecione um produto/serviço</option>';
            this.data.produtos.forEach(produto => {
                select.innerHTML += `<option value="${produto.id}">${produto.nome} - ${formatters.currency(produto.preco_venda)}</option>`;
            });
        });
    }

    setupForms() {
        // Form de novo agendamento
        const novoAgendamentoForm = document.getElementById('novoAgendamentoForm');
        if (novoAgendamentoForm) {
            novoAgendamentoForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleNovoAgendamento(e.target);
            });

            // Auto-preencher valor quando produto é selecionado
            const produtoSelect = novoAgendamentoForm.querySelector('select[name="produto_id"]');
            const valorInput = novoAgendamentoForm.querySelector('input[name="valor"]');
            
            if (produtoSelect && valorInput) {
                produtoSelect.addEventListener('change', (e) => {
                    const produtoId = e.target.value;
                    const produto = this.data.produtos.find(p => p.id == produtoId);
                    if (produto) {
                        valorInput.value = produto.preco_venda;
                    }
                });
            }
        }
    }

    async handleNovoAgendamento(form) {
        if (!validateForm(form)) {
            notifications.warning('Por favor, preencha todos os campos obrigatórios');
            return;
        }

        const formData = new FormData(form);
        const agendamento = Object.fromEntries(formData.entries());
        const isEditing = form.dataset.editingId;

        try {
            let result;
            if (isEditing) {
                result = await api.updateAgendamento(isEditing, agendamento);
                notifications.success('Agendamento atualizado com sucesso!');
            } else {
                result = await api.createAgendamento(agendamento);
                notifications.success('Agendamento criado com sucesso!');
            }

            // Atualizar dados locais
            await this.loadInitialData();

            // Atualizar calendário
            if (calendar) {
                calendar.loadAgendamentos();
            }

            // Fechar modal
            closeModal('novoAgendamentoModal');

            // Limpar formulário
            form.reset();
            delete form.dataset.editingId;

            // Restaurar título do modal
            const modalTitle = document.querySelector('#novoAgendamentoModal .modal-title');
            if (modalTitle) modalTitle.textContent = 'Novo Agendamento';

            // Atualizar dashboard se estiver na página
            if (this.currentPage === 'dashboard') {
                this.loadDashboard();
            }

            // Atualizar página de agendamentos se estiver ativa
            if (this.currentPage === 'agendamentos' && typeof aplicarFiltros === 'function') {
                aplicarFiltros();
            }
        } catch (error) {
            console.error('Erro ao salvar agendamento:', error);
            notifications.error('Erro ao salvar agendamento');
        }
    }

    initializeCalendar() {
        calendar = new Calendar('calendar-container');
    }

    async loadDashboard() {
        try {
            const stats = await api.getDashboardStats();
            
            // Atualizar estatísticas
            document.getElementById('totalClientes').textContent = stats.totalClientes || 0;
            document.getElementById('agendamentosHoje').textContent = stats.agendamentosHoje || 0;
            document.getElementById('agendamentosMes').textContent = stats.agendamentosMes || 0;
            document.getElementById('receitaMes').textContent = formatters.currency(stats.receitaMes);

            // Carregar próximos agendamentos
            this.loadProximosAgendamentos();
        } catch (error) {
            console.error('Erro ao carregar dashboard:', error);
            notifications.error('Erro ao carregar estatísticas do dashboard');
        }
    }

    loadProximosAgendamentos() {
        const container = document.getElementById('proximosAgendamentos');
        if (!container) return;

        // Filtrar agendamentos dos próximos 7 dias
        const hoje = new Date();
        const proximosSete = new Date();
        proximosSete.setDate(hoje.getDate() + 7);

        const proximosAgendamentos = this.data.agendamentos
            .filter(agendamento => {
                const dataVisita = new Date(agendamento.data_visita);
                return dataVisita >= hoje && dataVisita <= proximosSete;
            })
            .sort((a, b) => {
                const dateA = new Date(`${a.data_visita} ${a.hora_inicio}`);
                const dateB = new Date(`${b.data_visita} ${b.hora_inicio}`);
                return dateA - dateB;
            })
            .slice(0, 5);

        if (proximosAgendamentos.length === 0) {
            container.innerHTML = '<p class="text-center" style="color: var(--gray-500); padding: 2rem;">Nenhum agendamento nos próximos dias</p>';
            return;
        }

        container.innerHTML = proximosAgendamentos.map(agendamento => `
            <div class="agendamento-item" style="padding: 1rem; border-bottom: 1px solid var(--gray-200); cursor: pointer;" onclick="app.viewAgendamento(${agendamento.id})">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <div style="font-weight: 600; color: var(--gray-900);">${agendamento.cliente_nome}</div>
                        <div style="color: var(--gray-600); font-size: 0.875rem;">${agendamento.produto_nome}</div>
                        <div style="color: var(--gray-500); font-size: 0.8rem;">
                            ${formatters.date(agendamento.data_visita)} às ${formatters.time(agendamento.hora_inicio)}
                        </div>
                    </div>
                    <div>
                        <span class="badge badge-${this.getStatusBadgeClass(agendamento.status)}">${this.getStatusLabel(agendamento.status)}</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    getStatusBadgeClass(status) {
        const statusMap = {
            'agendado': 'info',
            'confirmado': 'success',
            'em_andamento': 'warning',
            'concluido': 'success',
            'cancelado': 'error'
        };
        return statusMap[status] || 'info';
    }

    getStatusLabel(status) {
        const statusMap = {
            'agendado': 'Agendado',
            'confirmado': 'Confirmado',
            'em_andamento': 'Em Andamento',
            'concluido': 'Concluído',
            'cancelado': 'Cancelado'
        };
        return statusMap[status] || status;
    }

    viewAgendamento(id) {
        const agendamento = this.data.agendamentos.find(a => a.id === id);
        if (agendamento) {
            // Implementar modal de visualização/edição de agendamento
            console.log('Visualizar agendamento:', agendamento);
        }
    }

    // Carregar páginas HTML externas
    async loadAgendamentosPage() {
        const page = document.getElementById('agendamentos-page');
        try {
            const response = await fetch('pages/agendamentos.html');
            const html = await response.text();
            page.innerHTML = html;

            // Executar scripts da página
            const scripts = page.querySelectorAll('script');
            scripts.forEach(script => {
                const newScript = document.createElement('script');
                newScript.textContent = script.textContent;
                document.head.appendChild(newScript);
                document.head.removeChild(newScript);
            });

            // Inicializar funcionalidades específicas da página
            this.initAgendamentosPage();
        } catch (error) {
            console.error('Erro ao carregar página de agendamentos:', error);
            page.innerHTML = `
                <div class="page-header">
                    <div>
                        <h1 class="page-title">Agendamentos</h1>
                        <p class="page-subtitle">Gerencie todos os agendamentos</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p>Erro ao carregar a página. Tente novamente.</p>
                    </div>
                </div>
            `;
        }
    }

    initAgendamentosPage() {
        // Popular filtro de funcionários
        const filtroFuncionario = document.getElementById('filtroFuncionario');
        if (filtroFuncionario) {
            filtroFuncionario.innerHTML = '<option value="">Todos os funcionários</option>';
            this.data.funcionarios.forEach(funcionario => {
                filtroFuncionario.innerHTML += `<option value="${funcionario.id}">${funcionario.nome}</option>`;
            });
        }

        // Carregar lista inicial
        if (typeof carregarListaAgendamentos === 'function') {
            carregarListaAgendamentos();
        }
    }

    async loadClientesPage() {
        const page = document.getElementById('clientes-page');
        try {
            const response = await fetch('pages/clientes.html');
            const html = await response.text();
            page.innerHTML = html;

            // Executar scripts da página
            const scripts = page.querySelectorAll('script');
            scripts.forEach(script => {
                const newScript = document.createElement('script');
                newScript.textContent = script.textContent;
                document.head.appendChild(newScript);
                document.head.removeChild(newScript);
            });

            // Inicializar funcionalidades específicas da página
            this.initClientesPage();
        } catch (error) {
            console.error('Erro ao carregar página de clientes:', error);
            page.innerHTML = `
                <div class="page-header">
                    <div>
                        <h1 class="page-title">Clientes</h1>
                        <p class="page-subtitle">Gerencie sua base de clientes</p>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="openModal('novoClienteModal')">
                            <i class="fas fa-plus"></i>
                            Novo Cliente
                        </button>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p>Erro ao carregar a página. Tente novamente.</p>
                    </div>
                </div>
            `;
        }
    }

    initClientesPage() {
        // Carregar lista inicial de clientes
        if (typeof carregarListaClientes === 'function') {
            carregarListaClientes();
        }
    }

    async loadProdutosPage() {
        const page = document.getElementById('produtos-page');
        try {
            const response = await fetch('pages/produtos.html');
            const html = await response.text();
            page.innerHTML = html;

            // Executar scripts da página
            const scripts = page.querySelectorAll('script');
            scripts.forEach(script => {
                const newScript = document.createElement('script');
                newScript.textContent = script.textContent;
                document.head.appendChild(newScript);
                document.head.removeChild(newScript);
            });

            // Inicializar funcionalidades específicas da página
            this.initProdutosPage();
        } catch (error) {
            console.error('Erro ao carregar página de produtos:', error);
            page.innerHTML = `
                <div class="page-header">
                    <div>
                        <h1 class="page-title">Produtos e Serviços</h1>
                        <p class="page-subtitle">Gerencie seu catálogo</p>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="openModal('novoProdutoModal')">
                            <i class="fas fa-plus"></i>
                            Novo Produto
                        </button>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p>Erro ao carregar a página. Tente novamente.</p>
                    </div>
                </div>
            `;
        }
    }

    initProdutosPage() {
        // Carregar lista inicial de produtos
        if (typeof carregarListaProdutos === 'function') {
            carregarListaProdutos();
        }
        if (typeof popularFiltroCategorias === 'function') {
            popularFiltroCategorias();
        }
    }

    loadFuncionariosPage() {
        const page = document.getElementById('funcionarios-page');
        page.innerHTML = `
            <div class="page-header">
                <div>
                    <h1 class="page-title">Funcionários</h1>
                    <p class="page-subtitle">Gerencie sua equipe</p>
                </div>
                <div>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Novo Funcionário
                    </button>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <p>Página de funcionários em desenvolvimento...</p>
                </div>
            </div>
        `;
    }

    async loadUsuariosPage() {
        const page = document.getElementById('usuarios-page');
        try {
            const response = await fetch('pages/usuarios.html');
            const html = await response.text();
            page.innerHTML = html;

            // Executar scripts da página
            const scripts = page.querySelectorAll('script');
            scripts.forEach(script => {
                const newScript = document.createElement('script');
                newScript.textContent = script.textContent;
                document.head.appendChild(newScript);
                document.head.removeChild(newScript);
            });

            // Inicializar funcionalidades específicas da página
            this.initUsuariosPage();
        } catch (error) {
            console.error('Erro ao carregar página de usuários:', error);
            page.innerHTML = `
                <div class="page-header">
                    <div>
                        <h1 class="page-title">Usuários</h1>
                        <p class="page-subtitle">Gerencie usuários do sistema</p>
                    </div>
                    <div>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Novo Usuário
                        </button>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        <p>Erro ao carregar a página. Tente novamente.</p>
                    </div>
                </div>
            `;
        }
    }

    initUsuariosPage() {
        // Carregar lista inicial de usuários
        if (typeof carregarListaUsuarios === 'function') {
            carregarListaUsuarios();
        }
    }

    loadRelatoriosPage() {
        const page = document.getElementById('relatorios-page');
        page.innerHTML = `
            <div class="page-header">
                <div>
                    <h1 class="page-title">Relatórios</h1>
                    <p class="page-subtitle">Análises e estatísticas</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <p>Página de relatórios em desenvolvimento...</p>
                </div>
            </div>
        `;
    }

    loadConfiguracoes() {
        const page = document.getElementById('configuracoes-page');
        page.innerHTML = `
            <div class="page-header">
                <div>
                    <h1 class="page-title">Configurações</h1>
                    <p class="page-subtitle">Configurações do sistema</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <p>Página de configurações em desenvolvimento...</p>
                </div>
            </div>
        `;
    }
}

// Inicializar aplicação quando DOM estiver carregado
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});
