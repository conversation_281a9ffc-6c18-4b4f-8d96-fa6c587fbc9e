<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Saúde Flex - Sistema de Agendamentos</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <a href="/" class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    Saúde Flex
                </a>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="/" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-chart-line nav-icon"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="agendamentos">
                        <i class="fas fa-calendar-alt nav-icon"></i>
                        Agendamentos
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="clientes">
                        <i class="fas fa-users nav-icon"></i>
                        Clientes
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="produtos">
                        <i class="fas fa-box nav-icon"></i>
                        Produtos/Serviços
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="funcionarios">
                        <i class="fas fa-user-tie nav-icon"></i>
                        Funcionários
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="relatorios">
                        <i class="fas fa-chart-bar nav-icon"></i>
                        Relatórios
                    </a>
                </li>
                <li class="nav-item admin-only">
                    <a href="#" class="nav-link" data-page="usuarios">
                        <i class="fas fa-user-shield nav-icon"></i>
                        Usuários
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="configuracoes">
                        <i class="fas fa-cog nav-icon"></i>
                        Configurações
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page active">
                <div class="page-header">
                    <div>
                        <h1 class="page-title">Dashboard</h1>
                        <p class="page-subtitle">Visão geral do sistema de agendamentos</p>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="openModal('novoAgendamentoModal')">
                            <i class="fas fa-plus"></i>
                            Novo Agendamento
                        </button>
                    </div>
                </div>

                <!-- Estatísticas -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="totalClientes">0</div>
                        <div class="stat-label">Clientes Ativos</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12% este mês
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value" id="agendamentosHoje">0</div>
                        <div class="stat-label">Agendamentos Hoje</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +5% vs ontem
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value" id="agendamentosMes">0</div>
                        <div class="stat-label">Agendamentos Este Mês</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +18% vs mês anterior
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value" id="receitaMes">R$ 0</div>
                        <div class="stat-label">Receita Este Mês</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +25% vs mês anterior
                        </div>
                    </div>
                </div>

                <!-- Calendário e Agendamentos Recentes -->
                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
                    <!-- Calendário -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Calendário de Agendamentos</h3>
                        </div>
                        <div class="card-body">
                            <div id="calendar-container">
                                <!-- Calendário será renderizado aqui -->
                            </div>
                        </div>
                    </div>

                    <!-- Agendamentos Recentes -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Próximos Agendamentos</h3>
                        </div>
                        <div class="card-body">
                            <div id="proximosAgendamentos">
                                <!-- Lista será carregada dinamicamente -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Outras páginas serão carregadas dinamicamente -->
            <div id="agendamentos-page" class="page"></div>
            <div id="clientes-page" class="page"></div>
            <div id="produtos-page" class="page"></div>
            <div id="funcionarios-page" class="page"></div>
            <div id="relatorios-page" class="page"></div>
            <div id="usuarios-page" class="page"></div>
            <div id="configuracoes-page" class="page"></div>
        </main>
    </div>

    <!-- Modal Novo Agendamento -->
    <div id="novoAgendamentoModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Novo Agendamento</h2>
                <button class="modal-close" onclick="closeModal('novoAgendamentoModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="novoAgendamentoForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Cliente</label>
                            <select class="form-control" name="cliente_id" required>
                                <option value="">Selecione um cliente</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Funcionário</label>
                            <select class="form-control" name="funcionario_id" required>
                                <option value="">Selecione um funcionário</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Produto/Serviço</label>
                            <select class="form-control" name="produto_id" required>
                                <option value="">Selecione um produto/serviço</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Valor</label>
                            <input type="number" class="form-control" name="valor" step="0.01" placeholder="0,00">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Data da Visita</label>
                            <input type="date" class="form-control" name="data_visita" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Hora de Início</label>
                            <input type="time" class="form-control" name="hora_inicio" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Hora de Fim</label>
                            <input type="time" class="form-control" name="hora_fim" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Endereço da Visita</label>
                        <textarea class="form-control" name="endereco_visita" rows="3" placeholder="Endereço completo para a visita"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Observações</label>
                        <textarea class="form-control" name="observacoes" rows="3" placeholder="Observações adicionais"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('novoAgendamentoModal')">
                    Cancelar
                </button>
                <button type="submit" form="novoAgendamentoForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Salvar Agendamento
                </button>
            </div>
        </div>
    </div>

    <!-- Container para notificações -->
    <div id="notifications-container"></div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/calendar.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
