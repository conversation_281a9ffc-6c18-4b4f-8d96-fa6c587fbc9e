<!-- Página de Produtos -->
<div class="page-header">
    <div>
        <h1 class="page-title">Produtos e Serviços</h1>
        <p class="page-subtitle">Gerencie seu catálogo com cálculo automático de preços</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="openModal('novoProdutoModal')">
            <i class="fas fa-plus"></i>
            Novo Produto
        </button>
    </div>
</div>

<!-- Filtros -->
<div class="card">
    <div class="card-body">
        <div class="filters-row">
            <div class="form-group">
                <label class="form-label">Buscar</label>
                <input type="text" id="buscarProduto" class="form-control" placeholder="Nome ou descrição...">
            </div>
            <div class="form-group">
                <label class="form-label">Categoria</label>
                <select id="filtroCategoria" class="form-control">
                    <option value="">Todas as categorias</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">Status</label>
                <select id="filtroStatus" class="form-control">
                    <option value="">Todos</option>
                    <option value="ativo">Ativo</option>
                    <option value="inativo">Inativo</option>
                </select>
            </div>
            <div class="form-group">
                <button class="btn btn-secondary" onclick="aplicarFiltrosProdutos()">
                    <i class="fas fa-search"></i>
                    Filtrar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Produtos -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Lista de Produtos</h3>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Nome</th>
                        <th>Categoria</th>
                        <th>Preço Custo</th>
                        <th>Lucro</th>
                        <th>Preço Venda</th>
                        <th>Estoque</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="listaProdutos">
                    <!-- Produtos serão carregados aqui -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Novo/Editar Produto -->
<div id="novoProdutoModal" class="modal">
    <div class="modal-content modal-lg">
        <div class="modal-header">
            <h2 class="modal-title">Novo Produto</h2>
            <button class="modal-close" onclick="closeModal('novoProdutoModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="novoProdutoForm">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Nome *</label>
                        <input type="text" class="form-control" name="nome" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Categoria</label>
                        <input type="text" class="form-control" name="categoria" placeholder="Ex: Nutrição, Suplementação">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Descrição</label>
                    <textarea class="form-control" name="descricao" rows="3" placeholder="Descrição detalhada do produto/serviço"></textarea>
                </div>
                
                <!-- Seção de Preços -->
                <div class="form-section">
                    <h4>Cálculo de Preços</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Preço de Custo (R$)</label>
                            <input type="number" class="form-control" name="preco_custo" step="0.01" min="0" value="0" onchange="calcularPrecoVenda()">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Tipo de Lucro</label>
                            <select class="form-control" name="tipo_lucro" onchange="calcularPrecoVenda()">
                                <option value="valor">Valor (R$)</option>
                                <option value="percentual">Percentual (%)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Lucro Desejado</label>
                            <input type="number" class="form-control" name="lucro_desejado" step="0.01" min="0" value="0" onchange="calcularPrecoVenda()">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Preço de Venda (R$)</label>
                            <input type="number" class="form-control" name="preco_venda" step="0.01" min="0" readonly style="background-color: #f8f9fa;">
                            <small class="form-text text-muted">Calculado automaticamente</small>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Margem de Lucro</label>
                            <input type="text" class="form-control" id="margemLucro" readonly style="background-color: #f8f9fa;">
                            <small class="form-text text-muted">Percentual sobre o custo</small>
                        </div>
                    </div>
                </div>
                
                <!-- Seção de Estoque -->
                <div class="form-section">
                    <h4>Controle de Estoque</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Estoque Atual</label>
                            <input type="number" class="form-control" name="estoque_atual" min="0" value="0">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Estoque Mínimo</label>
                            <input type="number" class="form-control" name="estoque_minimo" min="0" value="0">
                            <small class="form-text text-muted">Para alertas de reposição</small>
                        </div>
                    </div>
                </div>
                
                <!-- Outras Informações -->
                <div class="form-section">
                    <h4>Informações Adicionais</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Duração Estimada (minutos)</label>
                            <input type="number" class="form-control" name="duracao_estimada" min="0" placeholder="Para serviços">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Status</label>
                            <select class="form-control" name="status">
                                <option value="ativo">Ativo</option>
                                <option value="inativo">Inativo</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">URL da Foto</label>
                        <input type="url" class="form-control" name="foto" placeholder="https://exemplo.com/foto.jpg">
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('novoProdutoModal')">
                Cancelar
            </button>
            <button type="submit" form="novoProdutoForm" class="btn btn-primary">
                <i class="fas fa-save"></i>
                Salvar Produto
            </button>
        </div>
    </div>
</div>

<!-- Modal de Confirmação de Exclusão -->
<div id="confirmarExclusaoModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Confirmar Exclusão</h2>
            <button class="modal-close" onclick="closeModal('confirmarExclusaoModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <p>Tem certeza que deseja remover este produto?</p>
            <p><strong id="nomeProdutoExclusao"></strong></p>
            <p class="text-warning">Esta ação não pode ser desfeita.</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('confirmarExclusaoModal')">
                Cancelar
            </button>
            <button type="button" class="btn btn-danger" onclick="confirmarExclusaoProduto()">
                <i class="fas fa-trash"></i>
                Confirmar Exclusão
            </button>
        </div>
    </div>
</div>

<script>
let produtoParaExcluir = null;

// Função para calcular preço de venda automaticamente
function calcularPrecoVenda() {
    const form = document.getElementById('novoProdutoForm');
    const precoCusto = parseFloat(form.preco_custo.value) || 0;
    const lucroDesejado = parseFloat(form.lucro_desejado.value) || 0;
    const tipoLucro = form.tipo_lucro.value;
    
    let precoVenda = 0;
    let margemLucro = 0;
    
    if (tipoLucro === 'valor') {
        precoVenda = precoCusto + lucroDesejado;
    } else if (tipoLucro === 'percentual') {
        precoVenda = precoCusto * (1 + lucroDesejado / 100);
    }
    
    if (precoCusto > 0) {
        margemLucro = ((precoVenda - precoCusto) / precoCusto) * 100;
    }
    
    form.preco_venda.value = precoVenda.toFixed(2);
    document.getElementById('margemLucro').value = margemLucro.toFixed(2) + '%';
}

// Carregar lista de produtos
function carregarListaProdutos() {
    const tbody = document.getElementById('listaProdutos');
    tbody.innerHTML = '<tr><td colspan="8" class="text-center">Carregando...</td></tr>';
    
    // Usar dados já carregados no app
    if (app && app.data && app.data.produtos) {
        renderizarProdutos(app.data.produtos);
    } else {
        // Fallback para carregar via API
        api.getProdutos().then(produtos => {
            renderizarProdutos(produtos);
        }).catch(error => {
            console.error('Erro ao carregar produtos:', error);
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">Erro ao carregar produtos</td></tr>';
        });
    }
}

function renderizarProdutos(produtos) {
    const tbody = document.getElementById('listaProdutos');
    
    if (produtos.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center">Nenhum produto encontrado</td></tr>';
        return;
    }
    
    tbody.innerHTML = produtos.map(produto => `
        <tr>
            <td>
                <div style="font-weight: 600;">${produto.nome}</div>
                <div style="font-size: 0.875rem; color: var(--gray-600);">${produto.descricao || ''}</div>
            </td>
            <td>${produto.categoria || '-'}</td>
            <td>${formatters.currency(produto.preco_custo)}</td>
            <td>
                ${produto.tipo_lucro === 'percentual' ? 
                    produto.lucro_desejado + '%' : 
                    formatters.currency(produto.lucro_desejado)
                }
            </td>
            <td style="font-weight: 600;">${formatters.currency(produto.preco_venda)}</td>
            <td>
                <span class="${produto.estoque_atual <= produto.estoque_minimo ? 'text-danger' : ''}">
                    ${produto.estoque_atual || 0}
                </span>
                ${produto.estoque_atual <= produto.estoque_minimo ? 
                    '<i class="fas fa-exclamation-triangle text-warning" title="Estoque baixo"></i>' : ''
                }
            </td>
            <td>
                <span class="badge badge-${produto.status === 'ativo' ? 'success' : 'secondary'}">
                    ${produto.status === 'ativo' ? 'Ativo' : 'Inativo'}
                </span>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editarProduto(${produto.id})" title="Editar">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="excluirProduto(${produto.id}, '${produto.nome}')" title="Excluir">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// Aplicar filtros
function aplicarFiltrosProdutos() {
    const busca = document.getElementById('buscarProduto').value.toLowerCase();
    const categoria = document.getElementById('filtroCategoria').value;
    const status = document.getElementById('filtroStatus').value;

    let produtosFiltrados = app.data.produtos.filter(produto => {
        // Filtro de busca
        if (busca && !produto.nome.toLowerCase().includes(busca) &&
            !(produto.descricao && produto.descricao.toLowerCase().includes(busca))) {
            return false;
        }

        // Filtro de categoria
        if (categoria && produto.categoria !== categoria) return false;

        // Filtro de status
        if (status && produto.status !== status) return false;

        return true;
    });

    renderizarProdutos(produtosFiltrados);
}

// Popular filtro de categorias
function popularFiltroCategorias() {
    const categorias = [...new Set(app.data.produtos.map(p => p.categoria).filter(c => c))].sort();
    const select = document.getElementById('filtroCategoria');

    select.innerHTML = '<option value="">Todas as categorias</option>';
    categorias.forEach(categoria => {
        select.innerHTML += `<option value="${categoria}">${categoria}</option>`;
    });
}

// Editar produto
function editarProduto(id) {
    const produto = app.data.produtos.find(p => p.id === id);
    if (!produto) return;

    const form = document.getElementById('novoProdutoForm');

    // Preencher formulário
    Object.keys(produto).forEach(key => {
        const input = form.querySelector(`[name="${key}"]`);
        if (input) {
            input.value = produto[key] || '';
        }
    });

    // Calcular preço de venda
    calcularPrecoVenda();

    // Marcar como edição
    form.dataset.editingId = id;

    // Alterar título do modal
    document.querySelector('#novoProdutoModal .modal-title').textContent = 'Editar Produto';

    openModal('novoProdutoModal');
}

// Excluir produto
function excluirProduto(id, nome) {
    produtoParaExcluir = id;
    document.getElementById('nomeProdutoExclusao').textContent = nome;
    openModal('confirmarExclusaoModal');
}

// Confirmar exclusão
async function confirmarExclusaoProduto() {
    if (!produtoParaExcluir) return;

    try {
        await api.deleteProduto(produtoParaExcluir);
        notifications.success('Produto removido com sucesso!');

        // Atualizar dados
        await app.loadInitialData();
        carregarListaProdutos();
        popularFiltroCategorias();

        closeModal('confirmarExclusaoModal');
        produtoParaExcluir = null;

    } catch (error) {
        console.error('Erro ao excluir produto:', error);
        notifications.error('Erro ao excluir produto');
    }
}

// Configurar formulário
document.getElementById('novoProdutoForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    if (!validateForm(e.target)) {
        notifications.warning('Por favor, preencha todos os campos obrigatórios');
        return;
    }

    const formData = new FormData(e.target);
    const produto = Object.fromEntries(formData.entries());
    const isEditing = e.target.dataset.editingId;

    try {
        let result;
        if (isEditing) {
            result = await api.updateProduto(isEditing, produto);
            notifications.success('Produto atualizado com sucesso!');
        } else {
            result = await api.createProduto(produto);
            notifications.success('Produto cadastrado com sucesso!');
        }

        // Atualizar dados
        await app.loadInitialData();
        carregarListaProdutos();
        popularFiltroCategorias();

        // Fechar modal
        closeModal('novoProdutoModal');

        // Limpar formulário
        e.target.reset();
        delete e.target.dataset.editingId;

        // Restaurar título
        document.querySelector('#novoProdutoModal .modal-title').textContent = 'Novo Produto';

    } catch (error) {
        console.error('Erro ao salvar produto:', error);
        notifications.error('Erro ao salvar produto');
    }
});

// Inicializar página quando carregada
document.addEventListener('DOMContentLoaded', () => {
    if (typeof app !== 'undefined' && app.data && app.data.produtos) {
        carregarListaProdutos();
        popularFiltroCategorias();
    }
});

// Configurar eventos de filtro
document.getElementById('buscarProduto').addEventListener('input', aplicarFiltrosProdutos);
document.getElementById('filtroCategoria').addEventListener('change', aplicarFiltrosProdutos);
document.getElementById('filtroStatus').addEventListener('change', aplicarFiltrosProdutos);
</script>
