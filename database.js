const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Criar conexão com o banco de dados
const dbPath = path.join(__dirname, 'saude_flex.db');
const db = new sqlite3.Database(dbPath);

// Função para inicializar o banco de dados
function initializeDatabase() {
    db.serialize(() => {
        // Tabela de clientes
        db.run(`CREATE TABLE IF NOT EXISTS clientes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT UNIQUE,
            telefone TEXT,
            endereco TEXT,
            cidade TEXT,
            estado TEXT,
            cep TEXT,
            data_nascimento DATE,
            genero TEXT,
            observacoes TEXT,
            status TEXT DEFAULT 'ativo',
            data_cadastro DATETIME DEFAULT CURRENT_TIMESTAMP,
            data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP
        )`);

        // Verificar se a tabela produtos existe e tem a estrutura antiga
        db.get("SELECT sql FROM sqlite_master WHERE type='table' AND name='produtos'", (err, row) => {
            if (err) {
                console.error('Erro ao verificar tabela produtos:', err);
                return;
            }

            if (row && !row.sql.includes('preco_custo')) {
                console.log('Atualizando estrutura da tabela produtos...');

                // Fazer backup da tabela antiga
                db.run(`CREATE TABLE produtos_backup AS SELECT * FROM produtos`, (err) => {
                    if (err) {
                        console.error('Erro ao criar backup:', err);
                        return;
                    }

                    // Remover tabela antiga
                    db.run(`DROP TABLE produtos`, (err) => {
                        if (err) {
                            console.error('Erro ao remover tabela antiga:', err);
                            return;
                        }

                        // Criar nova tabela com estrutura completa
                        db.run(`CREATE TABLE produtos (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            nome TEXT NOT NULL,
                            descricao TEXT,
                            preco_custo DECIMAL(10,2) DEFAULT 0,
                            lucro_desejado DECIMAL(10,2) DEFAULT 0,
                            tipo_lucro TEXT DEFAULT 'valor', -- 'valor' ou 'percentual'
                            preco_venda DECIMAL(10,2) NOT NULL DEFAULT 0,
                            categoria TEXT,
                            estoque_atual INTEGER DEFAULT 0,
                            estoque_minimo INTEGER DEFAULT 0,
                            duracao_estimada INTEGER, -- em minutos
                            foto TEXT, -- URL ou caminho da foto
                            status TEXT DEFAULT 'ativo',
                            data_cadastro DATETIME DEFAULT CURRENT_TIMESTAMP,
                            data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP
                        )`, (err) => {
                            if (err) {
                                console.error('Erro ao criar nova tabela:', err);
                                return;
                            }

                            // Migrar dados antigos
                            db.run(`INSERT INTO produtos (id, nome, descricao, preco_venda, categoria, status, data_cadastro, data_atualizacao)
                                    SELECT id, nome,
                                           COALESCE(descricao, '') as descricao,
                                           COALESCE(preco, 0) as preco_venda,
                                           COALESCE(categoria, '') as categoria,
                                           COALESCE(status, 'ativo') as status,
                                           COALESCE(data_cadastro, CURRENT_TIMESTAMP) as data_cadastro,
                                           COALESCE(data_atualizacao, CURRENT_TIMESTAMP) as data_atualizacao
                                    FROM produtos_backup`, (err) => {
                                if (err) {
                                    console.error('Erro ao migrar dados:', err);
                                    return;
                                }

                                // Remover backup
                                db.run(`DROP TABLE produtos_backup`, (err) => {
                                    if (err) {
                                        console.error('Erro ao remover backup:', err);
                                    } else {
                                        console.log('Estrutura da tabela produtos atualizada com sucesso!');
                                    }
                                });
                            });
                        });
                    });
                });
            } else if (!row) {
                // Criar tabela produtos se não existir
                db.run(`CREATE TABLE produtos (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nome TEXT NOT NULL,
                    descricao TEXT,
                    preco_custo DECIMAL(10,2) DEFAULT 0,
                    lucro_desejado DECIMAL(10,2) DEFAULT 0,
                    tipo_lucro TEXT DEFAULT 'valor', -- 'valor' ou 'percentual'
                    preco_venda DECIMAL(10,2) NOT NULL DEFAULT 0,
                    categoria TEXT,
                    estoque_atual INTEGER DEFAULT 0,
                    estoque_minimo INTEGER DEFAULT 0,
                    duracao_estimada INTEGER, -- em minutos
                    foto TEXT, -- URL ou caminho da foto
                    status TEXT DEFAULT 'ativo',
                    data_cadastro DATETIME DEFAULT CURRENT_TIMESTAMP,
                    data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP
                )`);
            }
        });

        // Tabela de usuários (sistema de login)
        db.run(`CREATE TABLE IF NOT EXISTS usuarios (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            senha TEXT NOT NULL,
            tipo TEXT NOT NULL DEFAULT 'vendedor', -- admin, gerente, vendedor
            status TEXT DEFAULT 'ativo',
            ultimo_login DATETIME,
            data_cadastro DATETIME DEFAULT CURRENT_TIMESTAMP,
            data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP
        )`);

        // Tabela de funcionários/consultores
        db.run(`CREATE TABLE IF NOT EXISTS funcionarios (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            usuario_id INTEGER,
            nome TEXT NOT NULL,
            email TEXT UNIQUE,
            telefone TEXT,
            cargo TEXT,
            especialidades TEXT, -- JSON array
            status TEXT DEFAULT 'ativo',
            data_cadastro DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (usuario_id) REFERENCES usuarios (id)
        )`);

        // Tabela de agendamentos
        db.run(`CREATE TABLE IF NOT EXISTS agendamentos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER,
            funcionario_id INTEGER,
            produto_id INTEGER,
            data_visita DATE NOT NULL,
            hora_inicio TIME NOT NULL,
            hora_fim TIME NOT NULL,
            endereco_visita TEXT,
            status TEXT DEFAULT 'agendado', -- agendado, confirmado, em_andamento, concluido, cancelado
            observacoes TEXT,
            valor DECIMAL(10,2),
            forma_pagamento TEXT,
            data_agendamento DATETIME DEFAULT CURRENT_TIMESTAMP,
            data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cliente_id) REFERENCES clientes (id),
            FOREIGN KEY (funcionario_id) REFERENCES funcionarios (id),
            FOREIGN KEY (produto_id) REFERENCES produtos (id)
        )`);

        // Tabela de vendas
        db.run(`CREATE TABLE IF NOT EXISTS vendas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER,
            vendedor_id INTEGER,
            subtotal DECIMAL(10,2) NOT NULL,
            desconto_total DECIMAL(10,2) DEFAULT 0,
            total_final DECIMAL(10,2) NOT NULL,
            forma_pagamento TEXT,
            observacoes TEXT,
            data_venda DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cliente_id) REFERENCES clientes (id),
            FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
        )`);

        // Tabela de itens da venda
        db.run(`CREATE TABLE IF NOT EXISTS itens_venda (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            venda_id INTEGER,
            produto_id INTEGER,
            quantidade INTEGER NOT NULL DEFAULT 1,
            preco_unitario DECIMAL(10,2) NOT NULL,
            desconto_item DECIMAL(10,2) DEFAULT 0,
            subtotal_item DECIMAL(10,2) NOT NULL,
            FOREIGN KEY (venda_id) REFERENCES vendas (id),
            FOREIGN KEY (produto_id) REFERENCES produtos (id)
        )`);

        // Tabela de histórico de visitas
        db.run(`CREATE TABLE IF NOT EXISTS historico_visitas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            agendamento_id INTEGER,
            venda_id INTEGER,
            relatorio TEXT,
            produtos_apresentados TEXT, -- JSON array
            resultado TEXT, -- venda, reagendamento, sem_interesse
            valor_venda DECIMAL(10,2),
            observacoes TEXT,
            data_visita DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (agendamento_id) REFERENCES agendamentos (id),
            FOREIGN KEY (venda_id) REFERENCES vendas (id)
        )`);

        // Tabela de auditoria/logs
        db.run(`CREATE TABLE IF NOT EXISTS auditoria (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            usuario_id INTEGER,
            acao TEXT NOT NULL,
            tabela TEXT NOT NULL,
            registro_id INTEGER,
            dados_anteriores TEXT, -- JSON
            dados_novos TEXT, -- JSON
            ip_address TEXT,
            data_acao DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (usuario_id) REFERENCES usuarios (id)
        )`);

        // Inserir dados iniciais
        insertInitialData();
    });
}

function insertInitialData() {
    // Aguardar um pouco para garantir que as tabelas foram criadas
    setTimeout(() => {
        // Usuário administrador padrão
        const bcrypt = require('bcryptjs');
        const senhaAdmin = bcrypt.hashSync('admin123', 10);

        db.run(`INSERT OR IGNORE INTO usuarios (nome, email, senha, tipo) VALUES (?, ?, ?, ?)`,
            ['Administrador', '<EMAIL>', senhaAdmin, 'admin'], (err) => {
                if (err) {
                    console.error('Erro ao inserir usuário admin:', err);
                }
            });

        // Verificar se a tabela produtos existe antes de inserir dados
        db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='produtos'", (err, row) => {
            if (err) {
                console.error('Erro ao verificar tabela produtos:', err);
                return;
            }

            if (row) {
                // Produtos/Serviços iniciais (com novos campos)
                const produtos = [
                    ['Consulta Nutricional', 'Avaliação nutricional completa com plano alimentar personalizado', 50.00, 50.00, 'valor', 100.00, 'Nutrição', 10, 5, 60],
                    ['Suplementos Vitamínicos', 'Linha completa de vitaminas e minerais', 30.00, 59.90, 'valor', 89.90, 'Suplementação', 50, 10, 30],
                    ['Programa Detox', 'Programa completo de desintoxicação de 21 dias', 150.00, 149.00, 'valor', 299.00, 'Bem-estar', 20, 5, 45],
                    ['Avaliação Física', 'Avaliação corporal e plano de exercícios', 40.00, 80.00, 'valor', 120.00, 'Fitness', 15, 3, 90],
                    ['Produtos Naturais', 'Linha de produtos naturais e fitoterápicos', 25.00, 40.00, 'valor', 65.00, 'Fitoterapia', 30, 8, 30]
                ];

                const insertProduto = db.prepare(`INSERT OR IGNORE INTO produtos (nome, descricao, preco_custo, lucro_desejado, tipo_lucro, preco_venda, categoria, estoque_atual, estoque_minimo, duracao_estimada) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`);
                produtos.forEach(produto => {
                    insertProduto.run(produto, (err) => {
                        if (err) {
                            console.error('Erro ao inserir produto:', err);
                        }
                    });
                });
                insertProduto.finalize();
            }
        });

        // Funcionários iniciais
        const funcionarios = [
            ['Ana Silva', '<EMAIL>', '(11) 99999-1111', 'Consultora Sênior', '["Nutrição", "Suplementação"]'],
            ['Carlos Santos', '<EMAIL>', '(11) 99999-2222', 'Consultor', '["Fitness", "Bem-estar"]'],
            ['Maria Oliveira', '<EMAIL>', '(11) 99999-3333', 'Especialista', '["Fitoterapia", "Produtos Naturais"]']
        ];

        const insertFuncionario = db.prepare(`INSERT OR IGNORE INTO funcionarios (nome, email, telefone, cargo, especialidades) VALUES (?, ?, ?, ?, ?)`);
        funcionarios.forEach(funcionario => {
            insertFuncionario.run(funcionario, (err) => {
                if (err) {
                    console.error('Erro ao inserir funcionário:', err);
                }
            });
        });
        insertFuncionario.finalize();
    }, 1000); // Aguardar 1 segundo
}

// Executar inicialização se este arquivo for executado diretamente
if (require.main === module) {
    initializeDatabase();
    console.log('Banco de dados inicializado com sucesso!');
    db.close();
}

module.exports = { db, initializeDatabase };
